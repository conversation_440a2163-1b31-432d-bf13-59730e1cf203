# Nomad Project - Dummy Data Documentation

This document contains all the dummy/seed data that has been populated in the Nomad database for development and testing purposes.

## Database Connection Details

- **Database URL**: postgresql://nomad_database_user:<EMAIL>/nomad_database
- **Host**: dpg-d2hjlbripnbc73el8mu0-a.singapore-postgres.render.com
- **Port**: 5432
- **Database**: nomad_database
- **Username**: nomad_database_user
- **Password**: ZU7Stus7gGeRSbyFTnYz2TPczR1DMTZl

## Roles

| Role Name | Description |
|-----------|-------------|
| Admin | Full system access and management capabilities |
| Manager | Team management and project oversight |
| Employee | Standard employee access |
| HR | Human resources management |

## Companies

### 1. TechCorp Solutions
- **Email**: <EMAIL>
- **Phone**: ******-0101
- **Website**: https://techcorp.com
- **Address**: 123 Tech Street, San Francisco, CA 94105, USA
- **Description**: Leading technology solutions provider
- **Status**: Active

### 2. Innovate Inc
- **Email**: <EMAIL>
- **Phone**: ******-0202
- **Website**: https://innovateinc.com
- **Address**: 456 Innovation Ave, Austin, TX 73301, USA
- **Description**: Innovation-driven software development
- **Status**: Active

### 3. Global Tech Ltd
- **Email**: <EMAIL>
- **Phone**: +44-20-7946-0958
- **Website**: https://globaltech.com
- **Address**: 789 Global Plaza, London, England SW1A 1AA, UK
- **Description**: International technology consulting
- **Status**: Active

## Users

### TechCorp Solutions Employees

#### 1. John Smith (CEO)
- **Email**: <EMAIL>
- **Password**: password123
- **Phone**: ******-1001
- **Job Title**: CEO
- **Department**: Executive
- **Roles**: Admin
- **Status**: Active

#### 2. Sarah Johnson (CTO)
- **Email**: <EMAIL>
- **Password**: password123
- **Phone**: ******-1002
- **Job Title**: CTO
- **Department**: Technology
- **Roles**: Admin, Manager
- **Status**: Active

#### 3. Mike Davis (Senior Developer)
- **Email**: <EMAIL>
- **Password**: password123
- **Phone**: ******-1003
- **Job Title**: Senior Developer
- **Department**: Engineering
- **Roles**: Employee
- **Status**: Active

#### 4. Anna Taylor (UX Designer)
- **Email**: <EMAIL>
- **Password**: password123
- **Phone**: ******-1004
- **Job Title**: UX Designer
- **Department**: Design
- **Roles**: Employee
- **Status**: Active

### Innovate Inc Employees

#### 5. Emily Wilson (Product Manager)
- **Email**: <EMAIL>
- **Password**: password123
- **Phone**: ******-2001
- **Job Title**: Product Manager
- **Department**: Product
- **Roles**: Manager
- **Status**: Active

#### 6. David Brown (HR Director)
- **Email**: <EMAIL>
- **Password**: password123
- **Phone**: ******-2002
- **Job Title**: HR Director
- **Department**: Human Resources
- **Roles**: HR, Manager
- **Status**: Active

### Global Tech Ltd Employees

#### 7. Lisa Garcia (Software Engineer)
- **Email**: <EMAIL>
- **Password**: password123
- **Phone**: +44-20-7946-1001
- **Job Title**: Software Engineer
- **Department**: Engineering
- **Roles**: Employee
- **Status**: Active

#### 8. James Miller (Team Lead)
- **Email**: <EMAIL>
- **Password**: password123
- **Phone**: +44-20-7946-1002
- **Job Title**: Team Lead
- **Department**: Engineering
- **Roles**: Manager, Employee
- **Status**: Active

## Dashboards

### 1. Executive Dashboard
- **Owner**: John Smith (<EMAIL>)
- **Description**: High-level company metrics and KPIs
- **Visibility**: Shared
- **Default**: Yes
- **Widgets**: Revenue chart, Employee counter, Projects list
- **Layout**: 2x2 grid

### 2. Engineering Dashboard
- **Owner**: Sarah Johnson (<EMAIL>)
- **Description**: Development metrics and project status
- **Visibility**: Shared
- **Default**: No
- **Widgets**: Commits chart, Bugs counter, Sprints kanban
- **Layout**: 2x2 grid

### 3. HR Dashboard
- **Owner**: David Brown (<EMAIL>)
- **Description**: Human resources metrics and employee data
- **Visibility**: Private
- **Default**: No
- **Widgets**: Headcount counter, Turnover chart, Performance table
- **Layout**: 2x2 grid

## Test Credentials Summary

For quick reference during testing:

| User | Email | Password | Role | Company |
|------|-------|----------|------|---------|
| John Smith | <EMAIL> | password123 | Admin | TechCorp Solutions |
| Sarah Johnson | <EMAIL> | password123 | Admin, Manager | TechCorp Solutions |
| Mike Davis | <EMAIL> | password123 | Employee | TechCorp Solutions |
| Emily Wilson | <EMAIL> | password123 | Manager | Innovate Inc |
| David Brown | <EMAIL> | password123 | HR, Manager | Innovate Inc |
| Lisa Garcia | <EMAIL> | password123 | Employee | Global Tech Ltd |
| James Miller | <EMAIL> | password123 | Manager, Employee | Global Tech Ltd |
| Anna Taylor | <EMAIL> | password123 | Employee | TechCorp Solutions |

## Notes

- All user passwords are set to `password123` for development convenience
- The database uses UUID primary keys for all entities
- SSL is enabled for the database connection
- The application runs on port 3000 by default
- Database synchronization is enabled, so schema changes will be automatically applied

## Seeding Instructions

To re-seed the database with fresh dummy data:

```bash
npm run seed
```

This will clear all existing data and repopulate the database with the dummy data described above.
