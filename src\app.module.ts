import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { UsersModule } from './users/users.module';
import { CompaniesModule } from './companies/companies.module';
import { AuthModule } from './auth/auth.module';
import { RolesModule } from './roles/roles.module';
import { DashboardModule } from './dashboard/dashboard.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { User } from './users/entities/user.entity';
import { Role } from './roles/entities/role.entity';
import { Company } from './companies/entities/company.entity';
import { Dashboard } from './dashboard/entities/dashboard.entity';
import { SeedService } from './seed.service';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    TypeOrmModule.forRoot({
      type: 'postgres',
      url: process.env.DATABASE_URL,
      entities: [User, Role, Company, Dashboard],
      synchronize: process.env.NODE_ENV !== 'production',
      ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
      logging: process.env.NODE_ENV === 'development',
      extra: {
        max: 20,
        connectionTimeoutMillis: 2000,
      },
    }),
    TypeOrmModule.forFeature([User, Role, Company, Dashboard]),
    UsersModule,
    CompaniesModule,
    AuthModule,
    RolesModule,
    DashboardModule,
  ],
  controllers: [AppController],
  providers: [AppService, SeedService],
})

export class AppModule {}
