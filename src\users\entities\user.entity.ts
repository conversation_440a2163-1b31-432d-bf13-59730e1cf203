import { IsEmail } from 'class-validator';
import { Role } from 'src/roles/entities/role.entity';
import { Company } from 'src/companies/entities/company.entity';
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  Index,
  ManyToMany,
  ManyToOne,
  JoinTable,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity()
@Index('UNIQUE_PHONE_NO', ['phone'], { unique: true })
@Index('UNIQUE_EMAIL_ID', ['email'], { unique: true })
export class User {
  @PrimaryGeneratedColumn('uuid')
  ID?: string;

  @Column()
  fullName: string;

  @Column()
  @IsEmail({}, { message: 'Invalid email format' })
  email: string;

  @Column()
  password: string;

  @Column({ nullable: true })
  phone?: string;

  @Column({ nullable: true })
  jobTitle?: string;

  @Column({ nullable: true })
  department?: string;

  @Column({ default: 'active' })
  status: string; // active, inactive, suspended

  @CreateDateColumn({ name: 'created_at' }) // Automatically set the created_at timestamp
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' }) // Automatically set the updated_at timestamp
  updatedAt: Date;

  @ManyToMany(() => Role)
  @JoinTable()
  roles?: Role[];

  @ManyToOne(() => Company, (company) => company.employees)
  company?: Company;
}
