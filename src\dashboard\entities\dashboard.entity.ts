import { User } from 'src/users/entities/user.entity';
import {
  En<PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity()
export class Dashboard {
  @PrimaryGeneratedColumn('uuid')
  ID?: string;

  @Column()
  title: string;

  @Column({ nullable: true })
  description?: string;

  @Column('json', { nullable: true })
  widgets?: any; // JSON object containing widget configurations

  @Column('json', { nullable: true })
  layout?: any; // JSON object containing layout configuration

  @Column({ default: true })
  isDefault: boolean;

  @Column({ default: 'private' })
  visibility: string; // private, shared, public

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @ManyToOne(() => User)
  owner?: User;
}
