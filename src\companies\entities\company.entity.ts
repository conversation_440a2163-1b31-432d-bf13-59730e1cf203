import { IsEmail, IsUrl } from 'class-validator';
import { User } from 'src/users/entities/user.entity';
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  Index,
  OneToMany,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity()
@Index('UNIQUE_COMPANY_EMAIL', ['email'], { unique: true })
export class Company {
  @PrimaryGeneratedColumn('uuid')
  ID?: string;

  @Column()
  name: string;

  @Column({ nullable: true })
  description?: string;

  @Column()
  @IsEmail({}, { message: 'Invalid email format' })
  email: string;

  @Column({ nullable: true })
  phone?: string;

  @Column({ nullable: true })
  @IsUrl({}, { message: 'Invalid website URL' })
  website?: string;

  @Column({ nullable: true })
  address?: string;

  @Column({ nullable: true })
  city?: string;

  @Column({ nullable: true })
  state?: string;

  @Column({ nullable: true })
  country?: string;

  @Column({ nullable: true })
  postalCode?: string;

  @Column({ default: 'active' })
  status: string; // active, inactive, suspended

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @OneToMany(() => User, (user) => user.company)
  employees?: User[];
}
