# Nomad Application Deployment Guide

## Platform Recommendation: Render

**Why Render is the best choice for this application:**
- ✅ Full support for Node.js applications with persistent connections
- ✅ Built-in PostgreSQL database hosting
- ✅ Automatic SSL certificates
- ✅ Easy environment variable management
- ✅ Support for server-side rendering and static assets
- ✅ Session management with persistent storage

## Prerequisites

1. **GitHub Repository**: Ensure your code is pushed to a GitHub repository
2. **Render Account**: Sign up at [render.com](https://render.com)

## Deployment Steps

### Step 1: Create a Render Account and Connect GitHub

1. Go to [render.com](https://render.com) and sign up
2. Connect your GitHub account
3. Grant access to your repository

### Step 2: Deploy Using render.yaml (Recommended)

The application includes a `render.yaml` file for automated deployment:

1. **Push your code** to GitHub with the `render.yaml` file
2. **In Render Dashboard**:
   - Click "New" → "Blueprint"
   - Connect your GitHub repository
   - Select the repository containing your Nomad application
   - Ren<PERSON> will automatically detect the `render.yaml` file
   - Click "Apply" to start deployment

### Step 3: Manual Deployment (Alternative)

If you prefer manual setup:

#### 3.1 Create PostgreSQL Database
1. In Render Dashboard, click "New" → "PostgreSQL"
2. Configure:
   - **Name**: `nomad-database`
   - **Database**: `nomad_database`
   - **User**: `nomad_database_user`
   - **Region**: Choose closest to your users
   - **Plan**: Starter (free tier available)
3. Click "Create Database"
4. **Save the connection details** provided

#### 3.2 Create Web Service
1. Click "New" → "Web Service"
2. Connect your GitHub repository
3. Configure:
   - **Name**: `nomad-app`
   - **Environment**: `Node`
   - **Region**: Same as database
   - **Branch**: `main` (or your default branch)
   - **Build Command**: `npm install && npm run build`
   - **Start Command**: `npm run start:prod`

### Step 4: Configure Environment Variables

In your Render web service settings, add these environment variables:

#### Required Variables:
```
NODE_ENV=production
PORT=10000
SESSION_SECRET=your-secure-random-string-here
```

#### Database Variables (from your PostgreSQL service):
```
DATABASE_URL=postgresql://username:password@host:port/database
```

#### Optional Variables:
```
LOG_LEVEL=info
```

### Step 5: Deploy and Verify

1. **Deploy**: Click "Deploy Latest Commit" or push to trigger auto-deploy
2. **Monitor**: Watch the build logs for any errors
3. **Test**: Once deployed, visit your app URL
4. **Seed Database**: Use the admin interface or API to populate initial data

## Environment Variables Reference

| Variable | Description | Required | Example |
|----------|-------------|----------|---------|
| `NODE_ENV` | Environment mode | Yes | `production` |
| `PORT` | Application port | Yes | `10000` |
| `DATABASE_URL` | PostgreSQL connection string | Yes | `postgresql://user:pass@host:port/db` |
| `SESSION_SECRET` | Session encryption key | Yes | `your-secure-secret-key` |

| `LOG_LEVEL` | Logging level | No | `info` |

## Post-Deployment Tasks

### 1. Database Seeding
After successful deployment, seed your database:

**Option A: Using the application interface**
- Navigate to your deployed app
- Use the admin interface to create initial data

**Option B: Using the seed script (if needed)**
- Connect to your Render service shell
- Run: `npm run seed:prod`

### 2. Custom Domain (Optional)
1. In Render Dashboard → Your Service → Settings
2. Add your custom domain
3. Configure DNS records as instructed

### 3. SSL Certificate
- Render automatically provides SSL certificates
- Your app will be available at `https://your-app-name.onrender.com`

## Monitoring and Maintenance

### Logs
- View logs in Render Dashboard → Your Service → Logs
- Monitor for errors and performance issues

### Database Management
- Access database via Render Dashboard → PostgreSQL service
- Use provided connection details for external tools

### Updates
- Push to your connected GitHub branch to trigger auto-deployment
- Monitor deployment status in Render Dashboard

## Troubleshooting

### Common Issues:

1. **Build Failures**
   - Check Node.js version compatibility
   - Verify all dependencies are in `package.json`
   - Review build logs for specific errors

2. **Database Connection Issues**
   - Verify `DATABASE_URL` is correctly set
   - Ensure database service is running
   - Check SSL configuration

3. **Static Assets Not Loading**
   - Verify `nest-cli.json` assets configuration
   - Check build output includes `public` and `views` folders

4. **Session Issues**
   - Verify `SESSION_SECRET` is set
   - Check database connection for session store
   - Ensure `trust proxy` is enabled

### Getting Help:
- Check Render documentation: [render.com/docs](https://render.com/docs)
- Review application logs for specific error messages
- Verify environment variables are correctly set

## Security Considerations

1. **Environment Variables**: Never commit sensitive data to Git
2. **Session Secret**: Use a strong, random session secret
3. **Database**: Use strong passwords and restrict access
4. **HTTPS**: Always use HTTPS in production (automatic with Render)
5. **CORS**: Configure appropriate CORS settings for your domain

## Performance Optimization

1. **Database Connections**: Connection pooling is configured
2. **Static Assets**: Served efficiently through Express
3. **Session Store**: PostgreSQL session store for scalability
4. **Logging**: Configured for production environment

## Quick Deployment Checklist

- [ ] Code pushed to GitHub repository
- [ ] Render account created and GitHub connected
- [ ] PostgreSQL database created on Render
- [ ] Web service created with correct build/start commands
- [ ] Environment variables configured
- [ ] Application deployed successfully
- [ ] Database seeded with initial data
- [ ] Application tested and working
- [ ] Custom domain configured (optional)
- [ ] Monitoring and logging verified

## Alternative Platforms

While Render is recommended, here are other options:

### Railway
- Similar to Render with good PostgreSQL support
- Easy deployment with `railway.json`
- Good for Node.js applications

### Heroku
- Mature platform with extensive add-ons
- Requires `Procfile` for deployment
- More expensive but very reliable

### DigitalOcean App Platform
- Good performance and pricing
- Supports Docker and buildpacks
- Integrated with DigitalOcean services

**Note**: Vercel is NOT recommended for this application due to its serverless nature, which doesn't support persistent database connections and session management required by this NestJS application.

Your Nomad application is now ready for production deployment on Render!
