import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from './users/entities/user.entity';
import { Role } from './roles/entities/role.entity';
import { Company } from './companies/entities/company.entity';
import { Dashboard } from './dashboard/entities/dashboard.entity';

@Injectable()
export class SeedService {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(Role)
    private roleRepository: Repository<Role>,
    @InjectRepository(Company)
    private companyRepository: Repository<Company>,
    @InjectRepository(Dashboard)
    private dashboardRepository: Repository<Dashboard>,
  ) {}

  async seedDatabase() {
    console.log('Starting database seeding...');

    // Clear existing data
    await this.dashboardRepository.delete({});
    await this.userRepository.delete({});
    await this.companyRepository.delete({});
    await this.roleRepository.delete({});

    // Create roles
    const adminRole = await this.roleRepository.save({
      name: 'Admin',
      description: 'Full system access and management capabilities',
    });

    const managerRole = await this.roleRepository.save({
      name: 'Manager',
      description: 'Team management and project oversight',
    });

    const employeeRole = await this.roleRepository.save({
      name: 'Employee',
      description: 'Standard employee access',
    });

    const hrRole = await this.roleRepository.save({
      name: 'HR',
      description: 'Human resources management',
    });

    console.log('Roles created successfully');

    // Create companies
    const techCorp = await this.companyRepository.save({
      name: 'TechCorp Solutions',
      description: 'Leading technology solutions provider',
      email: '<EMAIL>',
      phone: '******-0101',
      website: 'https://techcorp.com',
      address: '123 Tech Street',
      city: 'San Francisco',
      state: 'CA',
      country: 'USA',
      postalCode: '94105',
      status: 'active',
    });

    const innovateInc = await this.companyRepository.save({
      name: 'Innovate Inc',
      description: 'Innovation-driven software development',
      email: '<EMAIL>',
      phone: '******-0202',
      website: 'https://innovateinc.com',
      address: '456 Innovation Ave',
      city: 'Austin',
      state: 'TX',
      country: 'USA',
      postalCode: '73301',
      status: 'active',
    });

    const globalTech = await this.companyRepository.save({
      name: 'Global Tech Ltd',
      description: 'International technology consulting',
      email: '<EMAIL>',
      phone: '+44-20-7946-0958',
      website: 'https://globaltech.com',
      address: '789 Global Plaza',
      city: 'London',
      state: 'England',
      country: 'UK',
      postalCode: 'SW1A 1AA',
      status: 'active',
    });

    console.log('Companies created successfully');

    // Create users
    const users = [
      {
        fullName: 'John Smith',
        email: '<EMAIL>',
        password: 'password123',
        phone: '******-1001',
        jobTitle: 'CEO',
        department: 'Executive',
        status: 'active',
        company: techCorp,
        roles: [adminRole],
      },
      {
        fullName: 'Sarah Johnson',
        email: '<EMAIL>',
        password: 'password123',
        phone: '******-1002',
        jobTitle: 'CTO',
        department: 'Technology',
        status: 'active',
        company: techCorp,
        roles: [adminRole, managerRole],
      },
      {
        fullName: 'Mike Davis',
        email: '<EMAIL>',
        password: 'password123',
        phone: '******-1003',
        jobTitle: 'Senior Developer',
        department: 'Engineering',
        status: 'active',
        company: techCorp,
        roles: [employeeRole],
      },
      {
        fullName: 'Emily Wilson',
        email: '<EMAIL>',
        password: 'password123',
        phone: '******-2001',
        jobTitle: 'Product Manager',
        department: 'Product',
        status: 'active',
        company: innovateInc,
        roles: [managerRole],
      },
      {
        fullName: 'David Brown',
        email: '<EMAIL>',
        password: 'password123',
        phone: '******-2002',
        jobTitle: 'HR Director',
        department: 'Human Resources',
        status: 'active',
        company: innovateInc,
        roles: [hrRole, managerRole],
      },
      {
        fullName: 'Lisa Garcia',
        email: '<EMAIL>',
        password: 'password123',
        phone: '+44-20-7946-1001',
        jobTitle: 'Software Engineer',
        department: 'Engineering',
        status: 'active',
        company: globalTech,
        roles: [employeeRole],
      },
      {
        fullName: 'James Miller',
        email: '<EMAIL>',
        password: 'password123',
        phone: '+44-20-7946-1002',
        jobTitle: 'Team Lead',
        department: 'Engineering',
        status: 'active',
        company: globalTech,
        roles: [managerRole, employeeRole],
      },
      {
        fullName: 'Anna Taylor',
        email: '<EMAIL>',
        password: 'password123',
        phone: '******-1004',
        jobTitle: 'UX Designer',
        department: 'Design',
        status: 'active',
        company: techCorp,
        roles: [employeeRole],
      },
    ];

    const savedUsers = [];
    for (const userData of users) {
      const user = await this.userRepository.save(userData);
      savedUsers.push(user);
    }

    console.log('Users created successfully');

    // Create dashboards
    const dashboards = [
      {
        title: 'Executive Dashboard',
        description: 'High-level company metrics and KPIs',
        widgets: {
          revenue: { type: 'chart', position: { x: 0, y: 0 } },
          employees: { type: 'counter', position: { x: 1, y: 0 } },
          projects: { type: 'list', position: { x: 0, y: 1 } },
        },
        layout: { columns: 2, rows: 2 },
        isDefault: true,
        visibility: 'shared',
        owner: savedUsers[0], // John Smith
      },
      {
        title: 'Engineering Dashboard',
        description: 'Development metrics and project status',
        widgets: {
          commits: { type: 'chart', position: { x: 0, y: 0 } },
          bugs: { type: 'counter', position: { x: 1, y: 0 } },
          sprints: { type: 'kanban', position: { x: 0, y: 1 } },
        },
        layout: { columns: 2, rows: 2 },
        isDefault: false,
        visibility: 'shared',
        owner: savedUsers[1], // Sarah Johnson
      },
      {
        title: 'HR Dashboard',
        description: 'Human resources metrics and employee data',
        widgets: {
          headcount: { type: 'counter', position: { x: 0, y: 0 } },
          turnover: { type: 'chart', position: { x: 1, y: 0 } },
          performance: { type: 'table', position: { x: 0, y: 1 } },
        },
        layout: { columns: 2, rows: 2 },
        isDefault: false,
        visibility: 'private',
        owner: savedUsers[4], // David Brown
      },
    ];

    for (const dashboardData of dashboards) {
      await this.dashboardRepository.save(dashboardData);
    }

    console.log('Dashboards created successfully');
    console.log('Database seeding completed!');
  }
}
